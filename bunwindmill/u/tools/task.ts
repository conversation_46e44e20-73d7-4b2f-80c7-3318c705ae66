import { PostgresAdapter, type PostgresAdapterConfig } from "./postgres.ts";

// 套利策略枚举
export enum ArbitrageStrategy {
  IOTX_PATH = "cex_to_dex",
  SOL_PATH = "dex_to_cex"
}

// 套利步骤枚举
export enum ArbitrageStep {
  BUY_TOKEN = "buy_token",
  WITHDRAW_TOKEN = "withdraw_token",
  SWAP_TOKEN = "swap_token",
  BRIDGE_TRANSFER = "bridge_transfer",
  UNWRAP_SOL = "unwrap_sol",
  TRANSFER_TO_CEX = "transfer_to_cex",
  SELL_TOKEN = "sell_token",
  COMPLETED = "completed",
  FAILED = "failed"
}

// 任务状态枚举
export enum TaskStatus {
  RUNNING = "running",
  COMPLETED = "completed",
  FAILED = "failed",
  TIMEOUT = "timeout"
}

// 步骤状态枚举
export enum StepStatus {
  PENDING = "pending",
  RUNNING = "running",
  COMPLETED = "completed",
  FAILED = "failed"
}

// 任务主表接口
export interface TaskEntity {
  id?: number;
  task_id: string;
  strategy: ArbitrageStrategy;
  current_step: ArbitrageStep;
  status: TaskStatus;
  usdt_amount: number;
  cex_sol_price?: number;
  cex_iotx_price?: number;
  dex_sol_price?: number;
  dex_iotx_price?: number;
  iotex_address?: string;
  solana_address?: string;
  retry_count: number;
  max_retries: number;
  last_error?: string;
  created_at?: Date;
  updated_at?: Date;
  completed_at?: Date;
  [key: string]: any;
}

// 任务详情表接口
export interface TaskDetailEntity {
  id?: number;
  task_id: string;
  step_name: ArbitrageStep;
  step_order: number;
  status: StepStatus;
  buy_order_data?: any;
  sell_order_data?: any;
  swap_result_data?: any;
  bridge_result_data?: any;
  transfer_data?: any;
  balance_data?: any;
  execution_result?: any;
  error_message?: string;
  transaction_hash?: string;
  input_amount?: number;
  output_amount?: number;
  fee_amount?: number;
  started_at?: Date;
  completed_at?: Date;
  created_at?: Date;
  updated_at?: Date;
  [key: string]: any;
}

// 任务适配器
export class TaskAdapter extends PostgresAdapter<TaskEntity> {
  constructor(config: Omit<PostgresAdapterConfig, 'tableName'> = {}) {
    super({
      ...config,
      tableName: 'task'
    });
  }

  /**
   * 根据task_id查找任务
   */
  async findByTaskId(taskId: string): Promise<TaskEntity | null> {
    const result = await this.findOne({ where: { task_id: taskId } });
    return result;
  }

  /**
   * 获取指定策略的运行中任务
   */
  async findRunningByStrategy(strategy: ArbitrageStrategy): Promise<TaskEntity[]> {
    const task =  await this.findOne({
      where: {
        strategy,
        status: TaskStatus.RUNNING
      }
    });

    return task ? [task] : [];
  }

  /**
   * 更新任务步骤
   */
  async updateStep(taskId: string, step: ArbitrageStep, retryCount?: number, error?: string): Promise<void> {
    const updateData: Partial<TaskEntity> = {
      current_step: step,
      updated_at: new Date()
    };

    if (retryCount !== undefined) {
      updateData.retry_count = retryCount;
    }

    if (error) {
      updateData.last_error = error;
    }

    if (step === ArbitrageStep.COMPLETED) {
      updateData.status = TaskStatus.COMPLETED;
      updateData.completed_at = new Date();
    } else if (step === ArbitrageStep.FAILED) {
      updateData.status = TaskStatus.FAILED;
      updateData.completed_at = new Date();
    }

    await this.update({
      where: { task_id: taskId },
      data: updateData
    });
  }
}

// 任务详情适配器
export class TaskDetailAdapter extends PostgresAdapter<TaskDetailEntity> {
  constructor(config: Omit<PostgresAdapterConfig, 'tableName'> = {}) {
    super({
      ...config,
      tableName: 'task_detail'
    });
  }

  /**
   * 获取任务的所有步骤
   */
  async findByTaskId(taskId: string): Promise<TaskDetailEntity[]> {
    const task = await this.findOne({
      where: { task_id: taskId },
    });
    return task ? [task] : [];
  }

  /**
   * 获取任务的特定步骤
   */
  async findStep(taskId: string, stepName: ArbitrageStep): Promise<TaskDetailEntity | null> {
    return await this.findOne({
      where: {
        task_id: taskId,
        step_name: stepName
      }
    });
  }

  /**
   * 更新步骤状态
   */
  async updateStepStatus(
    taskId: string,
    stepName: ArbitrageStep,
    status: StepStatus,
    data?: {
      execution_result?: any;
      error_message?: string;
      transaction_hash?: string;
      input_amount?: number;
      output_amount?: number;
      fee_amount?: number;
    }
  ): Promise<void> {
    const updateData: Partial<TaskDetailEntity> = {
      status,
      updated_at: new Date()
    };

    if (status === StepStatus.RUNNING) {
      updateData.started_at = new Date();
    } else if (status === StepStatus.COMPLETED || status === StepStatus.FAILED) {
      updateData.completed_at = new Date();
    }

    if (data) {
      Object.assign(updateData, data);
    }

    await this.update({
      where: { task_id: taskId, step_name: stepName },
      data: updateData
    });
  }
}
