import { Octokit } from "npm:octokit";
import { createClient } from "npm:@clickhouse/client";
import URL from 'npm:url';

const octokit = new Octokit({
  auth: '****************************************',
});

const parsedUrl = URL.parse('clickhouse://default:<EMAIL>:8443/default');

const db = createClient({
  url: `https://${parsedUrl?.hostname}`,
  username: 'default',
  password: 'TBNE93x1_IpL8',
});

async function searchMostStarredRepos(
  perPage: number = 100,
  page: number = 1,
  stars: number = 1000,
  step: number = 1000
) {
  try {
    let q = `stars:${stars}..${stars + step}`;
    if (stars == 30000) {
      q = `stars:30000..1000000`;
    }
    console.log(q, page);
    const response = await octokit.rest.search.repos({
      q,
      sort: "stars",
      order: "desc",
      per_page: perPage,
      page: page,
    });
    // 打印 api limit 信息
    console.log(`剩余请求次数: ${response.headers["x-ratelimit-remaining"]}`);
    console.log(`总结果数: ${response.data.total_count}`);
    return {
      items: response.data.items,
      totalCount: response.data.total_count
    };
  } catch (error) {
    console.error("Error fetching repositories:", error);
    throw error;
  }
}

// 使用示例
export async function main() {
  // 调整范围生成，每20为一个区间
  const maxStars = 1500;
  const step = 20;
  const ranges = [];
  
  for (let i = 1000; i <= maxStars; i += step) {
    ranges.push(i);
  }
  
  for (const startStars of ranges.reverse()) {
    let page = 1;
    let hasMoreResults = true;
    
    while (hasMoreResults && page <= 10) { // 最多处理10页
      console.log(`处理星标区间 ${startStars}..${startStars + step}，页码: ${page}`);
      
      const result = await searchMostStarredRepos(100, page, startStars, step);
      const topRepos = result.items;
      const totalCount = result.totalCount;
      
      const newRepos = topRepos.map((repo) => {
        return {
          name: repo.name,
          user_id: repo.owner?.id,
          user_name: repo.owner?.login,
          description: repo.description,
          full_name: repo.full_name,
          topics: repo.topics,
          url: repo.html_url,
          stars: repo.stargazers_count,
          language: repo.language,
          updated_at: repo.updated_at,
          created_at: repo.created_at,
          forks: repo.forks,
          watchers: repo.watchers,
          size: repo.size,
          open_issues: repo.open_issues,
          license: repo.license?.name,
          is_template: repo.is_template,
          pushed_at: repo.pushed_at,
        };
      });

      if (newRepos.length === 0) {
        hasMoreResults = false;
        break;
      }
      
      newRepos.forEach((repo) => {
        console.log(repo.name, repo.stars);
      });

      await db.insert({
        table: "repos_new",
        format: "JSONEachRow",
        values: newRepos,
      });

      console.log(`插入 ${newRepos.length} 条记录，当前页: ${page}`);
      
      if (newRepos.length === 100 && totalCount > page * 100) {
        page++;
        console.log(`发现更多结果，继续获取下一页 ${page}`);
      } else {
        hasMoreResults = false;
      }

      await new Promise((resolve) => setTimeout(resolve, 1_000));
    }
  }
}
