{"version": "4", "specifiers": {"npm:@ai-sdk/deepseek@*": "0.1.16_zod@3.24.2", "npm:ai@*": "4.1.63_zod@3.24.2", "npm:mongodb@*": "6.15.0", "npm:zod@*": "3.24.2"}, "npm": {"@ai-sdk/deepseek@0.1.16_zod@3.24.2": {"integrity": "sha512-46o2lxBFmaz4JXerJud+0T8Bi6z/dhNdKVMU8gQGo2rjkCwbr7lHs01dfJ2X6A+ST8P2TKaCBtz73R9MAHn4PA==", "dependencies": ["@ai-sdk/openai-compatible", "@ai-sdk/provider", "@ai-sdk/provider-utils", "zod"]}, "@ai-sdk/openai-compatible@0.1.16_zod@3.24.2": {"integrity": "sha512-4+dlSJnXRjadbjTJ/EBDTjYl5f6XEW0vEIxJ8AiHq96Gs+bMmMxmTowwMR6PianLheSEL++BJE4KPie1qH878g==", "dependencies": ["@ai-sdk/provider", "@ai-sdk/provider-utils", "zod"]}, "@ai-sdk/provider-utils@2.1.14_zod@3.24.2": {"integrity": "sha512-0fNQwstwqHP+cE9bP9nQXeEhY/qJRGW5Y+AQrvwzd0EI2+gH1GmwI1IC/gIOksmz/gRy4G+kXXD+Fbq4T/jCbA==", "dependencies": ["@ai-sdk/provider", "eventsource-parser", "nanoid", "secure-json-parse", "zod"]}, "@ai-sdk/provider@1.0.12": {"integrity": "sha512-88Uu1zJIE1UUOVJWfE2ybJXgiH8JJ97QY9fbmplErEbfa/k/1kF+tWMVAAJolF2aOGmazQGyQLhv4I9CCuVACw==", "dependencies": ["json-schema"]}, "@ai-sdk/react@1.1.24_zod@3.24.2": {"integrity": "sha512-20rVK34QCaEl/4u87/Y+JXt7eCzF60UwoJOjHJhlCdmFrbk6AfRsijv0ludA9ELArE+ulxkSf21L3FokkZ2Gmg==", "dependencies": ["@ai-sdk/provider-utils", "@ai-sdk/ui-utils", "swr", "throttleit", "zod"]}, "@ai-sdk/ui-utils@1.1.20_zod@3.24.2": {"integrity": "sha512-k+qQh1YA8/kH12d8KQFWjr0UxyS9nLibzbLr2OY6fzpfZg8xIAKlZeH952JIp2XwAp72tesgbsAmA15cDldtyw==", "dependencies": ["@ai-sdk/provider", "@ai-sdk/provider-utils", "zod", "zod-to-json-schema"]}, "@mongodb-js/saslprep@1.2.0": {"integrity": "sha512-+ywrb0AqkfaYuhHs6LxKWgqbh3I72EpEgESCw37o+9qPx9WTCkgDm2B+eMrwehGtHBWHFU4GXvnSCNiFhhausg==", "dependencies": ["sparse-bitfield"]}, "@opentelemetry/api@1.9.0": {"integrity": "sha512-3giAOQvZiH5F9bMlMiv8+GSPMeqg0dbaeo58/0SlA9sxSqZhnUtxzX9/2FzyhS9sWQf5S0GJE0AKBrFqjpeYcg=="}, "@types/diff-match-patch@1.0.36": {"integrity": "sha512-xFdR6tkm0MWvBfO8xXCSsinYxHcqkQUlcHeSpMC2ukzOb6lwQAfDmW+Qt0AvlGd8HpsS28qKsB+oPeJn9I39jg=="}, "@types/webidl-conversions@7.0.3": {"integrity": "sha512-CiJJvcRtIgzadHCYXw7dqEnMNRjhGZlYK05Mj9OyktqV8uVT8fD2BFOB7S1uwBE3Kj2Z+4UyPmFw/Ixgw/LAlA=="}, "@types/whatwg-url@11.0.5": {"integrity": "sha512-coYR071JRaHa+xoEvvYqvnIHaVqaYrLPbsufM9BF63HkwI5Lgmy2QR8Q5K/lYDYo5AK82wOvSOS0UsLTpTG7uQ==", "dependencies": ["@types/webidl-conversions"]}, "ai@4.1.63_zod@3.24.2": {"integrity": "sha512-XXAU/jbtNZOHkTh9ZO4+ttXFXRjcs/62jJHbpO2M8UiH2FVIDia4fy4D5l8jGKlYVzKeE7rx/qB2GtrMjcc07w==", "dependencies": ["@ai-sdk/provider", "@ai-sdk/provider-utils", "@ai-sdk/react", "@ai-sdk/ui-utils", "@opentelemetry/api", "eventsource-parser", "jsondiffpatch", "zod"]}, "bson@6.10.3": {"integrity": "sha512-MTxGsqgYTwfshYWTRdmZRC+M7FnG1b4y7RO7p2k3X24Wq0yv1m77Wsj0BzlPzd/IowgESfsruQCUToa7vbOpPQ=="}, "chalk@5.4.1": {"integrity": "sha512-zgVZuo2WcZgfUEmsn6eO3kINexW8RAE4maiQ8QNs8CtpPCSyMiYsULR3HQYkm3w8FIA3SberyMJMSldGsW+U3w=="}, "dequal@2.0.3": {"integrity": "sha512-0je+qPKHEMohvfRTCEo3CrPG6cAzAYgmzKyxRiYSSDkS6eGJdyVJm7WaYA5ECaAD9wLB2T4EEeymA5aFVcYXCA=="}, "diff-match-patch@1.0.5": {"integrity": "sha512-IayShXAgj/QMXgB0IWmKx+rOPuGMhqm5w6jvFxmVenXKIzRqTAAsbBPT3kWQeGANj3jGgvcvv4yK6SxqYmikgw=="}, "eventsource-parser@3.0.0": {"integrity": "sha512-T1C0XCUimhxVQzW4zFipdx0SficT651NnkR0ZSH3yQwh+mFMdLfgjABVi4YtMTtaL4s168593DaoaRLMqryavA=="}, "json-schema@0.4.0": {"integrity": "sha512-es94M3nTIfsEPisRafak+HDLfHXnKBhV3vU5eqPcS3flIWqcxJWgXHXiey3YrpaNsanY5ei1VoYEbOzijuq9BA=="}, "jsondiffpatch@0.6.0": {"integrity": "sha512-3QItJOXp2AP1uv7waBkao5nCvhEv+QmJAd38Ybq7wNI74Q+BBmnLn4EDKz6yI9xGAIQoUF87qHt+kc1IVxB4zQ==", "dependencies": ["@types/diff-match-patch", "chalk", "diff-match-patch"]}, "memory-pager@1.5.0": {"integrity": "sha512-ZS4Bp4r/Zoeq6+NLJpP+0Zzm0pR8whtGPf1XExKLJBAczGMnSi3It14OiNCStjQjM6NU1okjQGSxgEZN8eBYKg=="}, "mongodb-connection-string-url@3.0.2": {"integrity": "sha512-rMO7CGo/9BFwyZABcKAWL8UJwH/Kc2x0g72uhDWzG48URRax5TCIcJ7Rc3RZqffZzO/Gwff/jyKwCU9TN8gehA==", "dependencies": ["@types/whatwg-url", "whatwg-url"]}, "mongodb@6.15.0": {"integrity": "sha512-ifBhQ0rRzHDzqp9jAQP6OwHSH7dbYIQjD3SbJs9YYk9AikKEettW/9s/tbSFDTpXcRbF+u1aLrhHxDFaYtZpFQ==", "dependencies": ["@mongodb-js/saslprep", "bson", "mongodb-connection-string-url"]}, "nanoid@3.3.11": {"integrity": "sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w=="}, "punycode@2.3.1": {"integrity": "sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg=="}, "react@19.0.0": {"integrity": "sha512-V8AVnmPIICiWpGfm6GLzCR/W5FXLchHop40W4nXBmdlEceh16rCN8O8LNWm5bh5XUX91fh7KpA+W0TgMKmgTpQ=="}, "secure-json-parse@2.7.0": {"integrity": "sha512-6aU+Rwsezw7VR8/nyvKTx8QpWH9FrcYiXXlqC4z5d5XQBDRqtbfsRjnwGyqbi3gddNtWHuEk9OANUotL26qKUw=="}, "sparse-bitfield@3.0.3": {"integrity": "sha512-kvzhi7vqKTfkh0PZU+2D2PIllw2ymqJKujUcyPMd9Y75Nv4nPbGJZXNhxsgdQab2BmlDct1YnfQCguEvHr7VsQ==", "dependencies": ["memory-pager"]}, "swr@2.3.3_react@19.0.0": {"integrity": "sha512-dshNvs3ExOqtZ6kJBaAsabhPdHyeY4P2cKwRCniDVifBMoG/SVI7tfLWqPXriVspf2Rg4tPzXJTnwaihIeFw2A==", "dependencies": ["dequal", "react", "use-sync-external-store"]}, "throttleit@2.1.0": {"integrity": "sha512-nt6AMGKW1p/70DF/hGBdJB57B8Tspmbp5gfJ8ilhLnt7kkr2ye7hzD6NVG8GGErk2HWF34igrL2CXmNIkzKqKw=="}, "tr46@5.1.0": {"integrity": "sha512-IUWnUK7ADYR5Sl1fZlO1INDUhVhatWl7BtJWsIhwJ0UAK7ilzzIa8uIqOO/aYVWHZPJkKbEL+362wrzoeRF7bw==", "dependencies": ["punycode"]}, "use-sync-external-store@1.4.0_react@19.0.0": {"integrity": "sha512-9WXSPC5fMv61vaupRkCKCxsPxBocVnwakBEkMIHHpkTTg6icbJtg6jzgtLDm4bl3cSHAca52rYWih0k4K3PfHw==", "dependencies": ["react"]}, "webidl-conversions@7.0.0": {"integrity": "sha512-VwddBukDzu71offAQR975unBIGqfKZpM+8ZX6ySk8nYhVoo5CYaZyzt3YBvYtRtO+aoGlqxPg/B87NGVZ/fu6g=="}, "whatwg-url@14.2.0": {"integrity": "sha512-De72GdQZzNTUBBChsXueQUnPKDkg/5A5zp7pFDuQAj5UFoENpiACU0wlCvzpAGnTkj++ihpKwKyYewn/XNUbKw==", "dependencies": ["tr46", "webidl-conversions"]}, "zod-to-json-schema@3.24.4_zod@3.24.2": {"integrity": "sha512-0uNlcvgabyrni9Ag8Vghj21drk7+7tp7VTwwR7KxxXXc/3pbXz2PHlDgj3cICahgF1kHm4dExBFj7BXrZJXzig==", "dependencies": ["zod"]}, "zod@3.24.2": {"integrity": "sha512-lY7CDW43ECgW9u1TcT3IoXHflywfVqDYze4waEz812jR/bZ8FHDsl7pFQoSZTz5N+2NqRXs8GBwnAwo3ZNxqhQ=="}}}