import { MongoClient, ServerApiVersion } from "npm:mongodb";

// Replace the placeholder with your Atlas connection string
const uri =
  "mongodb+srv://1kgithub:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0";

// Create a MongoClient with a MongoClientOptions object to set the Stable API version
const client = new MongoClient(uri, {
  serverApi: {
    version: ServerApiVersion.v1,
    strict: true,
    deprecationErrors: true,
  },
});

async function run() {
  const result = await client
    .db("1kgithub")
    .collection("repos")
    .find({})
    .toArray();
  console.log(result);
}

run().catch(console.dir).finally(async () => {
  await client.close();
});
