import * as cheerio from "npm:cheerio";

export async function main() {
  const url = "https://github.com/Drakkar-Software/OctoBot";

  const response = await fetch(url);
  const html = await response.text();

  const $ = cheerio.load(html);

  const dependFileName = ["package.json", "requirements.txt", "go.mod"];

  const fragments = $(
    'script[type="application/json"][data-target="react-partial.embeddedData"]'
  ).toArray();

  const readme = fragments
    .filter((fragment) => $(fragment).text().includes("overview"))
    .map((fragment) => $(fragment).text());

  const data = JSON.parse(readme[readme.length - 1]);
  const branch = data.props.initialPayload.repo.defaultBranch;
  console.log("🚀 ~ main ~ data:", data);
  const depItems = data.props.initialPayload.tree.items.filter((item: any) =>
    dependFileName.includes(item.name)
  );
  if (depItems.length === 0) {
    console.log("没有找到依赖文件");
    return;
  }

  // https://raw.githubusercontent.com/Drakkar-Software/OctoBot/refs/heads/master/requirements.txt
  const rawPath = `https://raw.githubusercontent.com/${url.split("/")[3]}/${
    url.split("/")[4]
  }/refs/heads/${branch}/${depItems[0].path}`;
  console.log("🚀 ~ main ~ rawPath:", rawPath);
  const rawResponse = await fetch(rawPath);
  const rawData = await rawResponse.text();
  console.log(rawData);
}

main();
