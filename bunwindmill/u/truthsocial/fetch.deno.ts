import puppeteer, { <PERSON><PERSON> } from "npm:puppeteer";
import { addExtra } from "npm:puppeteer-extra";
import StealthPlugin from "npm:puppeteer-extra-plugin-stealth";

async function retry<T>(
  fn: () => Promise<T>,
  maxAttempts = 3,
  delay = 2000
): Promise<T> {
  let lastError: Error | null = null;

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;
      console.error(`Attempt ${attempt}/${maxAttempts} failed:`, error);

      if (attempt < maxAttempts) {
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
  }

  throw lastError;
}

async function fetchTruthSocialContent() {
  const proxyUrl = "http://proxy.apify.com:8000";
  const launchArgs = JSON.stringify({
    args: [`--proxy-server=${proxyUrl}`],
    headless: true,
    stealth: true,
  });

  const pptr = addExtra(puppeteer);
  pptr.use(StealthPlugin());

  const browser = await pptr.connect({
    browserWSEndpoint: `wss://browserless.iotex.one/?token=6R0W53R135510&launch=${launchArgs}`,
  });

  const c = `__cflb=0H28vTPqhjwKvpvovP7uB2bKEYha7QdoWhuSJmisYR5; _tq_id.TV-5427368145-1.4081=da6935d7a95e5daf.1748228559.0.1748239056..; __cf_bm=fVtlbJa2pcYPRwYGET3e6sk0cPG8c4tglrP3kqih6x4-1748239306-1.0.1.1-v9JzRN_3MiUvjXHNhKvFW6X8ajcyk2DX177mYUiKh8Q8ixInr_LlN75eNJSCb1sZSYtoOyu3JbZYjiuDoKqwerk3_ByHQFmUJzGwXeycIlM; _cfuvid=4MZkMBH2P54gghr7thAlXVo25g5G3e_BZhQhhkfH7lE-1748239306020-*******-604800000; _mastodon_session=LmRSjvaljIxJm95lKcWhGyYUnzUSyF4JTv7pwgXiqzRfJVcNjAAN%2FwWJitB0XJveRmjQMSm0IV7rTOgRVT8W7baOq%2BoJ23iY%2F8PVFDOF6wYX48FRzcFeev8NTHDGHnBYKjBK8rhyA4G%2BDzeHiayqKkxrQnedkuEjGSpV%2BSvUiyEjNJtDf9bVR2pzVvR05ZFXlAPn0mGy%2Ftl3%2F94QPLDxyvJFcnCsOd0%2BzKUWSygev7Pi%2FzxKbssUYyDlgAMPl33IAwm9i2LVTVwjNHzL9uxQa%2BNGyEY7KiE44%2BaCUTY7a7y%2BT6w8FYWe1OvQADEoJyXxHvBkNWoBM8muEbh4cTFx%2B1Is%2BzX7PJhE3i7xOIjImGDTvSxD5rsEOTh%2BjS3uwbfSTLtIPpTIuTLshIXV8bU4j%2FVMbxlv--dHzyis6sHhvPja2F--ghiIOLXhgrFdq7lh%2FO9mtQ%3D%3D; mp_15c0cd079bcfa80cd935f3a1b8606b48_mixpanel=%7B%22distinct_id%22%3A%20%22wnxupgqe%22%2C%22%24device_id%22%3A%20%221970a8accd73360-0b415a28d6fe908-19525636-1fa400-1970a8accd8348e%22%2C%22%24search_engine%22%3A%20%22google%22%2C%22%24initial_referrer%22%3A%20%22https%3A%2F%2Fwww.google.com%2F%22%2C%22%24initial_referring_domain%22%3A%20%22www.google.com%22%2C%22%24user_id%22%3A%20%22wnxupgqe%22%7D`;
  const domain = "truthsocial.com";
  const cookie = c.split(";").map((item) => {
    const [key, value] = item.split("=");
    return {
      name: key.trim(),
      value: value.trim(),
      domain,
      path: "/",
    };
  });
  await browser.setCookie(...cookie);
  const page = await browser.newPage();

  await page.authenticate({
    username: "groups-RESIDENTIAL",
    password: "apify_proxy_7hs2Qf8KzRltUOAlxkkbx319dDkOYP0s3kNM",
  });

  // cookie
  try {
    await retry(async () => {
      try {
        await page.goto("https://truthsocial.com/", {
          waitUntil: "networkidle0",
          timeout: 60000,
        });
        return true; // 成功时返回
      } catch (error) {
        console.error("Navigation failed:", error);
        throw error; // 显式抛出错误以触发重试
      }
    });
  } catch (error) {
    console.error("All goto attempts failed:", error);
    await page.close();
    await browser.close();
    return null;
  }

  const responses = await page.evaluate(async () => {
    const res = await fetch('https://truthsocial.com/api/v1/accounts/107780257626128497/statuses?exclude_replies=true&only_replies=false&with_muted=true');
    return res.json();
  });

  await page.close();
  await browser.close();
  return responses;
}

// 使用示例
export async function main() {
  const r = await fetchTruthSocialContent();
  console.log(r);
}
