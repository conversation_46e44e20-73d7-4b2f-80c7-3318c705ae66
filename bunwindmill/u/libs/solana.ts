import { Connection, PublicKey, Keypair, Transaction, sendAndConfirmTransaction, SystemProgram } from '@solana/web3.js@1.98.0';
import { createCloseAccountInstruction } from '@solana/spl-token@0.4.13';
import BigNumber from 'bignumber.js@9.3.0';
import bs58 from 'bs58@6.0.0';

/**
 * Solana 链操作服务
 * 负责与 Solana 链的交互和余额查询
 */
export class SolanaService {
  private connection: Connection;
  private keypair?: Keypair;
  private readonly SOL_DECIMALS = 9;

  constructor(config: {
    rpcUrl?: string;
    privateKey?: string;
  }) {
    this.connection = new Connection(
      config.rpcUrl || 'https://api.mainnet-beta.solana.com',
      'confirmed'
    );

    if (config.privateKey) {
      this.initializeWallet(config.privateKey);
    }
  }
  /**
   * 转账 SOL 到指定地址
   * @param toAddress 接收方地址
   * @param amount 转账金额（SOL 单位）
   * @returns 交易签名
   */
  async transfer(toAddress: string, amount: number): Promise<string> {
    if (!this.keypair) {
      throw new Error('钱包未初始化，请提供私钥');
    }

    try {
      // 验证接收方地址
      const recipientPubKey = new PublicKey(toAddress);
      
      // 将 SOL 转换为 lamports
      const lamports = new BigNumber(amount)
        .multipliedBy(new BigNumber(10).pow(this.SOL_DECIMALS))
        .integerValue()
        .toNumber();

      // 构建转账交易
      const transaction = new Transaction().add(
        SystemProgram.transfer({
          fromPubkey: this.keypair.publicKey,
          toPubkey: recipientPubKey,
          lamports: lamports
        })
      );

      // 发送并确认交易
      const signature = await sendAndConfirmTransaction(
        this.connection,
        transaction,
        [this.keypair]
      );

      console.log('✅ 转账成功:', {
        signature,
        amount,
        from: this.keypair.publicKey.toString(),
        to: toAddress
      });

      return signature;
    } catch (error) {
      console.error('转账失败:', {
        error: (error as Error).message,
        amount,
        to: toAddress
      });
      throw error;
    }
  }

  private initializeWallet(privateKey: string) {
    try {
      // 尝试解析 base58 格式的私钥
      const secretKey = bs58.decode(privateKey);
      this.keypair = Keypair.fromSecretKey(secretKey);
    } catch (error) {
      console.error('Failed to initialize Solana wallet:', error);
      throw new Error('Invalid Solana private key format');
    }
  }

  /**
   * 获取 SOL 余额（格式化为 SOL 单位）
   * @param walletAddress 钱包地址，如果不提供则使用初始化的钱包地址
   */
  async getSolBalance(walletAddress?: string): Promise<number> {
    const address = walletAddress || this.keypair?.publicKey.toString();
    if (!address) {
      throw new Error('No wallet address provided and keypair not initialized');
    }

    try {
      const publicKey = new PublicKey(address);
      const balance = await this.connection.getBalance(publicKey);
      const balanceSOL = new BigNumber(balance)
        .dividedBy(new BigNumber(10).pow(this.SOL_DECIMALS))
        .toNumber();
      
      return balanceSOL;
    } catch (error) {
      console.error('Failed to get SOL balance:', {
        error: (error as Error).message,
        address,
      });
      throw error;
    }
  }

  /**
   * 获取 WSOL (Wrapped SOL) 余额
   * @param walletAddress 钱包地址，如果不提供则使用初始化的钱包地址
   * @returns WSOL 余额（以 SOL 为单位）
   */
  async getWsolBalance(walletAddress?: string): Promise<number> {
    const address = walletAddress || this.keypair?.publicKey.toString();
    if (!address) {
      throw new Error('No wallet address provided and keypair not initialized');
    }

    try {
      const publicKey = new PublicKey(address);
      // WSOL mint 地址
      const WSOL_MINT = new PublicKey('So11111111111111111111111111111111111111112');

      // 获取所有 WSOL 关联账户
      const resp = await this.connection.getTokenAccountsByOwner(publicKey, {
        mint: WSOL_MINT,
      });

      let totalWsolBalance = 0;

      for (const { account } of resp.value) {
        // Account data layout: offset 64–72 存储 token amount（u64 LE）
        const amount = Number(account.data.readBigUInt64LE(64));
        totalWsolBalance += amount;
      }

      // 转换为 SOL 单位
      const balanceSOL = new BigNumber(totalWsolBalance)
        .dividedBy(new BigNumber(10).pow(this.SOL_DECIMALS))
        .toNumber();

      return balanceSOL;
    } catch (error) {
      console.error('Failed to get WSOL balance:', {
        error: (error as Error).message,
        address,
      });
      throw error;
    }
  }

  /**
   * 获取总 SOL 余额（native SOL + WSOL）
   * @param walletAddress 钱包地址，如果不提供则使用初始化的钱包地址
   * @returns 总 SOL 余额
   */
  async getTotalSolBalance(walletAddress?: string): Promise<{ native: number; wsol: number; total: number }> {
    const nativeBalance = await this.getSolBalance(walletAddress);
    const wsolBalance = await this.getWsolBalance(walletAddress);

    return {
      native: nativeBalance,
      wsol: wsolBalance,
      total: nativeBalance + wsolBalance
    };
  }

  /**
   * 获取钱包地址
   */
  getWalletAddress(): string | undefined {
    return this.keypair?.publicKey.toString();
  }

  /**
   * 检查是否已初始化钱包
   */
  isWalletInitialized(): boolean {
    return !!this.keypair;
  }

  /**
   * 获取连接状态
   */
  async getConnectionInfo() {
    try {
      const version = await this.connection.getVersion();
      const slot = await this.connection.getSlot();
      return {
        rpcUrl: this.connection.rpcEndpoint,
        version,
        currentSlot: slot,
        status: 'connected'
      };
    } catch (error) {
      return {
        rpcUrl: this.connection.rpcEndpoint,
        status: 'disconnected',
        error: (error as Error).message
      };
    }
  }

  /**
   * 初始化连接检查
   */
  async initialize(): Promise<void> {
    try {
      const connectionInfo = await this.getConnectionInfo();
      console.log('Connected to Solana network:', connectionInfo);

      if (this.keypair) {
        const balance = await this.getSolBalance();
        console.log('Solana wallet balance:', {
          balance: balance,
          address: this.keypair.publicKey.toString(),
        });
      }
    } catch (error) {
      console.error('Failed to initialize SolanaService:', {
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Unwrap WSOL (Wrapped SOL) 回到原生 SOL
   * 关闭所有 WSOL 关联账户，将余额转换为原生 SOL
   * @returns 交易签名
   */
  async unwrapSol(): Promise<string> {
    if (!this.keypair) {
      throw new Error('Wallet not initialized. Please provide a private key.');
    }

    try {
      // WSOL mint 地址
      const WSOL_MINT = new PublicKey('So11111111111111111111111111111111111111112');
      
      // 获取所有 WSOL 关联账户
      const resp = await this.connection.getTokenAccountsByOwner(this.keypair.publicKey, {
        mint: WSOL_MINT,
      });

      if (resp.value.length === 0) {
        console.log('🔍 账户下没有找到任何 WSOL 关联账户。');
        throw new Error('No WSOL token accounts found');
      }

      // 构造关闭指令
      const tx = new Transaction();
      let totalAmount = 0;
      
      for (const { pubkey, account } of resp.value) {
        // Account data layout: offset 64–72 存储 token amount（u64 LE）
        const amount = Number(account.data.readBigUInt64LE(64));
        if (amount === 0) continue;

        totalAmount += amount;
        tx.add(
          createCloseAccountInstruction(
            pubkey,                    // 要关闭的 WSOL token 账户
            this.keypair.publicKey,    // SOL 收款方（解封后的 SOL 回到这里）
            this.keypair.publicKey     // 账户拥有者
          )
        );
      }

      if (tx.instructions.length === 0) {
        console.log('✅ 所有 WSOL 账户均为空，无需执行 unwrap。');
        throw new Error('All WSOL accounts are empty, no unwrap needed');
      }

      // 发送并确认交易
      const signature = await sendAndConfirmTransaction(this.connection, tx, [this.keypair]);
      
      const unwrappedSOL = new BigNumber(totalAmount)
        .dividedBy(new BigNumber(10).pow(this.SOL_DECIMALS))
        .toNumber();
      
      console.log('🚀 Unwrap 成功:', {
        signature,
        unwrappedAmount: unwrappedSOL,
        walletAddress: this.keypair.publicKey.toString()
      });

      return signature;
    } catch (error) {
      console.error('Failed to unwrap SOL:', {
        error: (error as Error).message,
        walletAddress: this.keypair?.publicKey.toString()
      });
      throw error;
    }
  }
}


export async function main() {
  const solana = new SolanaService({
    rpcUrl: 'https://api.mainnet-beta.solana.com',
    privateKey: '2P2nX7fuTxH5mxF5V8RM69t75MKwhoUghpz9znida5tAQujYpGWvLN3uV2PGBdqJju8iFZo11DrPAaPkxP6RrEqf'
  });

  const balance = await solana.getTotalSolBalance();
  console.log("🚀 ~ main ~ balance:", balance)
}
