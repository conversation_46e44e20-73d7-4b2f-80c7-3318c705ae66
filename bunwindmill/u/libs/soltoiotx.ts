import {
    Connection,
    <PERSON>Key,
    Keypair,
    TransactionMessage,
    VersionedTransaction,
    ComputeBudgetProgram,
    SystemProgram,
    TransactionInstruction
  } from '@solana/web3.js@1.98.0';
  import {
    getAssociatedTokenAddress,
    createApproveInstruction,
    TOKEN_PROGRAM_ID,
    NATIVE_MINT,
    createInitializeAccountInstruction
  } from '@solana/spl-token@0.4.13';
  import BigNumber from 'bignumber.js';
  import * as bs58 from 'bs58@6.0.0';
  import * as borsh from 'borsh@0.7.0';
  import { createHash } from 'crypto';
  import fetch from 'node-fetch@2.6.12';
  
  // TypeScript 类型定义
  
  interface CTokenAccountState {
    token: Buffer | Uint8Array;
    token_mint: Buffer | Uint8Array;
    min?: bigint;
    max?: bigint;
  }
  
  interface BalanceResult {
    balance: number;
    balanceSOL: BigNumber;
  }
  
  // 日志工具类
  class Logger {
    private static readonly PREFIX = '[Solana→IoTeX]';
  
    static info(message: string, ...args: any[]) {
      console.log(`${this.PREFIX} ℹ️  ${message}`, ...args);
    }
  
    static success(message: string, ...args: any[]) {
      console.log(`${this.PREFIX} ✅ ${message}`, ...args);
    }
  
    static warning(message: string, ...args: any[]) {
      console.warn(`${this.PREFIX} ⚠️  ${message}`, ...args);
    }
  
    static error(message: string, ...args: any[]) {
      console.error(`${this.PREFIX} ❌ ${message}`, ...args);
    }
  
    static step(step: number, message: string, ...args: any[]) {
      console.log(`${this.PREFIX} 📋 步骤 ${step}: ${message}`, ...args);
    }
  
    static transaction(message: string, signature?: string) {
      if (signature) {
        console.log(`${this.PREFIX} 🔗 ${message}`);
        console.log(`${this.PREFIX} 📝 交易签名: ${signature}`);
        console.log(`${this.PREFIX} 🔍 查看交易: ${SolanaToIoTeXConfig.SOLSCAN_BASE_URL}${signature}`);
      } else {
        console.log(`${this.PREFIX} 🔗 ${message}`);
      }
    }
  
    static balance(label: string, amount: string, symbol: string = 'SOL') {
      console.log(`${this.PREFIX} 💰 ${label}: ${amount} ${symbol}`);
    }
  
    static fee(label: string, amount: string, symbol: string = 'SOL') {
      console.log(`${this.PREFIX} 💸 ${label}: ${amount} ${symbol}`);
    }
  }
  
  // 配置常量
  class SolanaToIoTeXConfig {
    static readonly SOLANA_RPC_URL = 'https://api.mainnet-beta.solana.com';
    static readonly BRIDGE_CONFIG = 'E83sFGG5R3psWhotZSihbPb6DLmLnGK122ZF9q7VmYSM';
    static readonly BRIDGE_PROGRAM_ID = 'A9SGRcytnfx6U1QrnMUwK5sxYyCYY3MpyrPcyeafhSMF';
    static readonly SOL_DECIMALS = 9;
    static readonly SOL_CTOKEN = '3qdPWqm2iEYkTXRk3XGkuabEeXfentCUwJP1cCWrkre5';
    static readonly SPL_ACCOUNT_LAYOUT_SPAN = 165;
    static readonly MIN_TRANSFER_AMOUNT = 0.001;
    static readonly PRIORITY_FEE_MICRO_LAMPORTS = 1000;
    static readonly MAX_RETRIES = 3;
    static readonly BRIDGE_SUBMIT_URL = 'https://bridge.iotex.io/solana-submit';
    static readonly SOLSCAN_BASE_URL = 'https://solscan.io/tx/';
    static readonly INSTRUCTION_VARIANT_BRIDGE = 6;
  }
  
  // 向后兼容的 CONFIG 对象
  const CONFIG = {
    SOLANA_RPC_URL: SolanaToIoTeXConfig.SOLANA_RPC_URL,
    BRIDGE: {
      config: SolanaToIoTeXConfig.BRIDGE_CONFIG,
      programId: SolanaToIoTeXConfig.BRIDGE_PROGRAM_ID
    },
    SOL_TOKEN: {
      name: 'SOL',
      symbol: 'SOL',
      decimals: SolanaToIoTeXConfig.SOL_DECIMALS,
      cToken: SolanaToIoTeXConfig.SOL_CTOKEN
    },
    SPL_ACCOUNT_LAYOUT_SPAN: SolanaToIoTeXConfig.SPL_ACCOUNT_LAYOUT_SPAN,
    MIN_TRANSFER_AMOUNT: SolanaToIoTeXConfig.MIN_TRANSFER_AMOUNT
  };
  
  // 工具类和数据结构
  class Assignable {
    constructor(properties: Record<string, any>) {
      Object.keys(properties).forEach((key) => {
        (this as any)[key] = properties[key];
      });
    }
  }
  
  class BridgePayload extends Assignable {
    id: number;
    amount: bigint;
    recipient: string;
    payload: number[];
  
    constructor(properties: {
      id: number;
      amount: bigint;
      recipient: string;
      payload: number[];
    }) {
      super(properties);
      this.id = properties.id;
      this.amount = properties.amount;
      this.recipient = properties.recipient;
      this.payload = properties.payload;
    }
  }
  
  class AccountSeed {
    publicKey: PublicKey;
    seed: string;
  
    constructor(publicKey: PublicKey, seed: string) {
      this.publicKey = publicKey;
      this.seed = seed;
    }
  }
  
  class cTokenAccount extends Assignable {
    initialized: number;
    bump_seed: number;
    token_program_id: Uint8Array;
    config: Uint8Array;
    token: Uint8Array;
    token_mint: Uint8Array;
    destination: number;
    index: bigint;
    max: bigint;
    min: bigint;
  
    constructor(properties: {
      initialized: number;
      bump_seed: number;
      token_program_id: Uint8Array;
      config: Uint8Array;
      token: Uint8Array;
      token_mint: Uint8Array;
      destination: number;
      index: bigint;
      max: bigint;
      min: bigint;
    }) {
      super(properties);
      this.initialized = properties.initialized;
      this.bump_seed = properties.bump_seed;
      this.token_program_id = properties.token_program_id;
      this.config = properties.config;
      this.token = properties.token;
      this.token_mint = properties.token_mint;
      this.destination = properties.destination;
      this.index = properties.index;
      this.max = properties.max;
      this.min = properties.min;
    }
  }
  
  // 数据结构定义 - 使用静态常量
  const INSTRUCTION_VARIANT = {
    Bridge: SolanaToIoTeXConfig.INSTRUCTION_VARIANT_BRIDGE
  };
  
  const BRIDGE_PAYLOAD_SCHEMA = new Map([
    [BridgePayload, {
      kind: 'struct',
      fields: [
        ['id', 'u8'],
        ['amount', 'u64'],
        ['recipient', 'string'],
        ['payload', ['u8']]
      ]
    }]
  ]);
  
  const CTOKEN_ACCOUNT_SCHEMA = new Map([
    [cTokenAccount, {
      kind: 'struct',
      fields: [
        ['initialized', 'u8'],
        ['bump_seed', 'u8'],
        ['token_program_id', [32]],
        ['config', [32]],
        ['token', [32]],
        ['token_mint', [32]],
        ['destination', 'u32'],
        ['index', 'u64'],
        ['max', 'u64'],
        ['min', 'u64']
      ]
    }]
  ]);
  
  // 工具函数
  function generatePubKey({ fromPublicKey, programId = TOKEN_PROGRAM_ID }: { fromPublicKey: PublicKey; programId?: PublicKey }): AccountSeed {
    const seed = Keypair.generate().publicKey.toBase58().slice(0, 32);
    const publicKey = createWithSeed(fromPublicKey, seed, programId);
    return new AccountSeed(publicKey, seed);
  }
  
  function createWithSeed(fromPublicKey: PublicKey, seed: string, programId: PublicKey): PublicKey {
    const buffer = Buffer.concat([fromPublicKey.toBuffer(), Buffer.from(seed), programId.toBuffer()]);
    const hash = createHash('sha256').update(buffer).digest();
    return new PublicKey(new Uint8Array(hash));
  }
  
  async function createWrapSolAccountInstructions(connection: Connection, payer: PublicKey, owner: PublicKey, amount: BigNumber, account: AccountSeed): Promise<TransactionInstruction[]> {
    const balanceNeeded = await connection.getMinimumBalanceForRentExemption(CONFIG.SPL_ACCOUNT_LAYOUT_SPAN);
    const lamports = amount.plus(new BigNumber(balanceNeeded));
    const newAccount = account instanceof AccountSeed ? account : generatePubKey({ fromPublicKey: payer, programId: TOKEN_PROGRAM_ID });
    
    return [
      SystemProgram.createAccountWithSeed({
        fromPubkey: payer,
        basePubkey: payer,
        seed: newAccount.seed,
        newAccountPubkey: newAccount.publicKey,
        lamports: lamports.toNumber(),
        space: CONFIG.SPL_ACCOUNT_LAYOUT_SPAN,
        programId: TOKEN_PROGRAM_ID
      }),
      createInitializeAccountInstruction(newAccount.publicKey, NATIVE_MINT, owner)
    ];
  }
  
  function createBridgeInstruction(cToken: PublicKey, config: PublicKey, cTokenAccount: PublicKey, userAccount: PublicKey, userTransferAuthority: PublicKey, tokenMint: PublicKey, tokenProgramInfo: PublicKey, amount: bigint, recipient: string, payload: Uint8Array | null, cTokenProgramId: PublicKey): TransactionInstruction {
    const keys = [
      { pubkey: cToken, isSigner: false, isWritable: true },
      { pubkey: cTokenAccount, isSigner: false, isWritable: true },
      { pubkey: userAccount, isSigner: false, isWritable: true },
      { pubkey: userTransferAuthority, isSigner: true, isWritable: false },
      { pubkey: tokenMint, isSigner: false, isWritable: true },
      { pubkey: tokenProgramInfo, isSigner: false, isWritable: false },
      { pubkey: config, isSigner: false, isWritable: false }
    ];
  
    const bridgeData = new BridgePayload({
      id: INSTRUCTION_VARIANT.Bridge,
      amount: amount,
      recipient: recipient,
      payload: Array.from(payload || [])
    });
  
    return new TransactionInstruction({
      keys,
      programId: cTokenProgramId,
      data: Buffer.from(borsh.serialize(BRIDGE_PAYLOAD_SCHEMA, bridgeData))
    });
  }
  
  // 验证和解析函数
  function validateArgs(privateKeyBase58: string, iotexTargetAddress: string, amountSOL: string | number): { privateKeyBase58: string; iotexTargetAddress: string; transferAmountSOL: number } {
    // 验证转账金额
    const transferAmountSOL = parseFloat(amountSOL.toString());
    if (isNaN(transferAmountSOL) || transferAmountSOL <= 0) {
      throw new Error('转账金额格式错误，请输入有效的正数');
    }
  
    if (transferAmountSOL < CONFIG.MIN_TRANSFER_AMOUNT) {
      throw new Error(`转账金额过小，最小转账金额为 ${CONFIG.MIN_TRANSFER_AMOUNT} SOL`);
    }
  
    // 验证 IoTeX 地址格式
    if (!iotexTargetAddress.startsWith('0x') || iotexTargetAddress.length !== 42) {
      throw new Error('IoTeX 地址格式错误，地址应该以 0x 开头且长度为 42 个字符');
    }
  
    return { privateKeyBase58, iotexTargetAddress, transferAmountSOL };
  }
  
  function createKeypairFromPrivateKey(privateKeyBase58: string): Keypair {
    try {
      const privateKeyBytes = bs58.default.decode(privateKeyBase58);
      return Keypair.fromSecretKey(privateKeyBytes);
    } catch (error) {
      throw new Error('私钥格式错误，请使用 base58 格式的私钥');
    }
  }
  
  async function checkBalance(connection: Connection, keypair: Keypair): Promise<BalanceResult> {
    const balance = await connection.getBalance(keypair.publicKey);
    const balanceSOL = new BigNumber(balance).dividedBy(new BigNumber(10).pow(SolanaToIoTeXConfig.SOL_DECIMALS));
    Logger.balance('当前余额', balanceSOL.toFixed());
    return { balance, balanceSOL };
  }
  
  async function calculateTransactionFee(connection: Connection): Promise<BigNumber> {
    Logger.step(1, '计算交易费用');
  
    const baseTransactionFee = 5000;
    const computeUnits = 200000;
    const priorityFeePerUnit = 1000;
    const priorityFeeAmount = Math.ceil((priorityFeePerUnit * computeUnits) / 1000000);
    const rentExemption = await connection.getMinimumBalanceForRentExemption(CONFIG.SPL_ACCOUNT_LAYOUT_SPAN);
  
    const estimatedFee = new BigNumber(baseTransactionFee + priorityFeeAmount + rentExemption);
  
    Logger.info('费用明细');
    Logger.fee('基础交易费用', `${baseTransactionFee} lamports`);
    Logger.fee('优先费用', `${priorityFeeAmount} lamports (${priorityFeePerUnit} microlamports/CU × ${computeUnits} CU)`);
    Logger.fee('租金豁免', `${rentExemption} lamports`);
    Logger.fee('总计', `${estimatedFee.toString()} lamports`);
  
    return estimatedFee;
  }
  
  async function validateTransferLimits(connection: Connection, transferAmount: BigNumber): Promise<CTokenAccountState> {
    const cTokenPublicKey = new PublicKey(CONFIG.SOL_TOKEN.cToken);
    const cTokenData = await connection.getAccountInfo(cTokenPublicKey, 'confirmed');
  
    if (!cTokenData) {
      throw new Error('无法获取跨链桥代币账户信息');
    }
  
    try {
      const cTokenAccountState = borsh.deserialize(CTOKEN_ACCOUNT_SCHEMA, cTokenAccount, cTokenData.data);
      
      // 检查最小转账金额
      if (cTokenAccountState.min) {
        const minAmount = new BigNumber(cTokenAccountState.min.toString());
        const minAmountSOL = minAmount.dividedBy(new BigNumber(10).pow(CONFIG.SOL_TOKEN.decimals));
        Logger.info(`跨链桥最小转账金额: ${minAmountSOL.toFixed(6)} SOL`);
  
        if (transferAmount.isLessThan(minAmount)) {
          throw new Error(`转账金额过小！最小转账金额: ${minAmountSOL.toFixed(6)} SOL`);
        }
      }
  
      // 检查最大转账金额
      if (cTokenAccountState.max) {
        const maxAmount = new BigNumber(cTokenAccountState.max.toString());
        const maxAmountSOL = maxAmount.dividedBy(new BigNumber(10).pow(CONFIG.SOL_TOKEN.decimals));
        Logger.info(`跨链桥最大转账金额: ${maxAmountSOL.toFixed(6)} SOL`);
  
        if (transferAmount.isGreaterThan(maxAmount)) {
          throw new Error(`转账金额过大！最大转账金额: ${maxAmountSOL.toFixed(6)} SOL`);
        }
      }
      
      return cTokenAccountState;
    } catch (error) {
      Logger.error('解析跨链桥账户数据失败', error instanceof Error ? error.message : String(error));
      Logger.warning('尝试跳过账户数据解析，使用默认配置');
      return {
        token: Buffer.from(new PublicKey(CONFIG.SOL_TOKEN.cToken).toBytes()),
        token_mint: Buffer.from(new PublicKey(CONFIG.SOL_TOKEN.cToken).toBytes())
      };
    }
  }
  
  async function submitTransactionToBridge(signature: string, transaction: any): Promise<void> {
    if (!transaction || !transaction.slot) {
      Logger.warning('无法获取交易区块高度，跨链可能无法自动完成');
      return;
    }

    const blockHeight = transaction.slot;
    const txHash = Buffer.from(bs58.default.decode(signature)).toString('base64');

    Logger.step(2, '提交交易哈希到跨链桥');
    try {
      const submitResponse = await fetch(SolanaToIoTeXConfig.BRIDGE_SUBMIT_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        },
        body: JSON.stringify({
          height: blockHeight,
          txHash: txHash
        })
      });

      const submitResult = await submitResponse.json();
      if (submitResult.success) {
        Logger.success('交易哈希已成功提交到跨链桥');
        Logger.success('跨链转账已完成，请等待代币到达 IoTeX 地址');
        Logger.info('请注意: 跨链转账需要一定时间处理，请耐心等待代币到达 IoTeX 地址');
        Logger.info('您可以在 IoTeXScan 中查看接收地址的余额变化');
      } else {
        Logger.warning('交易哈希提交失败，但交易已确认。请手动检查跨链状态');
        Logger.info('提交响应', submitResult);
      }
    } catch (submitError) {
      Logger.warning('提交交易哈希时出错', submitError instanceof Error ? submitError.message : String(submitError));
      Logger.info('交易已确认，但可能需要手动处理跨链步骤');
    }
  }

  /**
   * 手动提交交易到跨链桥
   * @param {string} transactionSignature - Solana 交易签名
   * @param {string} [rpcUrl] - 可选的自定义 RPC URL
   * @returns {Promise<{success: boolean, message: string, txHash?: string, blockHeight?: number}>}
   */
  export async function manualSubmitToBridge(
    transactionSignature: string,
    rpcUrl?: string
  ): Promise<{success: boolean, message: string, txHash?: string, blockHeight?: number}> {
    try {
      // 验证交易签名格式
      if (!transactionSignature || typeof transactionSignature !== 'string') {
        return {
          success: false,
          message: '无效的交易签名'
        };
      }

      // 创建连接
      const connection = new Connection(rpcUrl || SolanaToIoTeXConfig.SOLANA_RPC_URL, 'confirmed');

      // 获取交易详情
      const transaction = await connection.getTransaction(transactionSignature, {
        commitment: 'confirmed',
        maxSupportedTransactionVersion: 0
      });

      if (!transaction) {
        return {
          success: false,
          message: '无法找到指定的交易，请确认交易签名正确且交易已确认'
        };
      }

      if (!transaction.slot) {
        return {
          success: false,
          message: '无法获取交易区块高度'
        };
      }

      const blockHeight = transaction.slot;
      const txHash = Buffer.from(bs58.default.decode(transactionSignature)).toString('base64');

      // 提交到跨链桥
      const submitResponse = await fetch(SolanaToIoTeXConfig.BRIDGE_SUBMIT_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        },
        body: JSON.stringify({
          height: blockHeight,
          txHash: txHash
        })
      });

      const submitResult = await submitResponse.json();

      if (submitResult.success) {
        return {
          success: true,
          message: '交易哈希已成功提交到跨链桥',
          txHash: txHash,
          blockHeight: blockHeight
        };
      } else {
        return {
          success: false,
          message: `提交失败: ${JSON.stringify(submitResult)}`,
          txHash: txHash,
          blockHeight: blockHeight
        };
      }

    } catch (error: any) {
      return {
        success: false,
        message: `提交过程中发生错误: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }
  
  /**
   * 从 Solana 转账指定数量的 SOL 到 IoTeX 地址的主函数
   * @param {string} privateKeyBase58 - Solana 私钥 (base58 格式)
   * @param {string} iotexTargetAddress - IoTeX 目标地址
   * @param {string|number} amountSOL - 转账金额 (SOL)
   */
  export async function transferSOLFromSolanaToIoTeX(privateKeyBase58: string, iotexTargetAddress: string, amountSOL: string | number): Promise<void> {
    const { transferAmountSOL } = validateArgs(privateKeyBase58, iotexTargetAddress, amountSOL);
  
    Logger.info('🚀 开始 Solana → IoTeX 跨链转账');
  
    try {
      // 连接到 Solana 网络
      const connection = new Connection(CONFIG.SOLANA_RPC_URL, 'confirmed');
  
      // 创建密钥对
      const keypair = createKeypairFromPrivateKey(privateKeyBase58);
  
      Logger.info('发送方地址 (Solana)', keypair.publicKey.toString());
      Logger.info('接收方地址 (IoTeX)', iotexTargetAddress);
      Logger.info('转账金额', `${transferAmountSOL} SOL`);
  
      // 转账金额（lamports）
      const transferAmount = new BigNumber(transferAmountSOL).multipliedBy(new BigNumber(10).pow(CONFIG.SOL_TOKEN.decimals));
  
      // 检查余额
      const { balance, balanceSOL } = await checkBalance(connection, keypair);
  
      // 计算交易费用
      const estimatedFee = await calculateTransactionFee(connection);
      const estimatedFeeSOL = estimatedFee.dividedBy(new BigNumber(10).pow(CONFIG.SOL_TOKEN.decimals));
      Logger.fee('预估交易费用', `${estimatedFeeSOL.toFixed(6)} SOL`);
      
      // 检查余额是否足够
      const requiredBalance = transferAmount.plus(estimatedFee);
      const requiredBalanceSOL = requiredBalance.dividedBy(new BigNumber(10).pow(CONFIG.SOL_TOKEN.decimals));
  
      if (new BigNumber(balance).isLessThan(requiredBalance)) {
        const shortfall = requiredBalanceSOL.minus(balanceSOL);
        Logger.error(`余额不足！需要 ${requiredBalanceSOL.toFixed(6)} SOL，当前 ${balanceSOL.toFixed(6)} SOL，缺少 ${shortfall.toFixed(6)} SOL`);
        throw new Error(`余额不足！缺少 ${shortfall.toFixed(6)} SOL`);
      }
  
      // 验证转账限制
      const cTokenAccountState = await validateTransferLimits(connection, transferAmount);
  
      Logger.step(3, '准备发送跨链转账交易');
      Logger.info('代币', CONFIG.SOL_TOKEN.symbol);
      Logger.info('转账金额', `${transferAmountSOL} SOL`);
      Logger.info('目标地址', iotexTargetAddress);
      Logger.fee('预估总费用', `${estimatedFeeSOL.toFixed(6)} SOL`);
  
      // 创建交易指令
      const instructions: TransactionInstruction[] = [];
  
      // 获取或创建 WSOL 代币账户
      let tokenAccount;
      try {
        tokenAccount = await getAssociatedTokenAddress(NATIVE_MINT, keypair.publicKey);
        const accountInfo = await connection.getAccountInfo(tokenAccount);
        if (!accountInfo) {
          // 如果关联代币账户不存在，创建一个临时账户来包装SOL
          const newAccount = generatePubKey({
            fromPublicKey: keypair.publicKey,
            programId: TOKEN_PROGRAM_ID
          });
          tokenAccount = newAccount.publicKey;
  
          // 添加包装 SOL 的指令
          const wrapInstructions = await createWrapSolAccountInstructions(connection, keypair.publicKey, keypair.publicKey, transferAmount, newAccount);
          instructions.push(...wrapInstructions);
        } else {
          // 检查现有代币账户的余额
          const tokenAccountInfo = await connection.getTokenAccountBalance(tokenAccount);
          const currentBalance = new BigNumber(tokenAccountInfo.value.amount);
          
          if (currentBalance.isLessThan(transferAmount)) {
            // 如果现有账户余额不足，创建一个新账户包装完整的转账金额
            const additionalAmount = transferAmount.minus(currentBalance);
            console.log(`现有WSOL余额不足，需要额外包装 ${additionalAmount.dividedBy(new BigNumber(10).pow(CONFIG.SOL_TOKEN.decimals)).toFixed(6)} SOL`);
            console.log(`将创建新账户包装完整的转账金额: ${transferAmount.dividedBy(new BigNumber(10).pow(CONFIG.SOL_TOKEN.decimals)).toFixed(6)} SOL`);
            
            // 创建一个新的临时账户来包装完整的转账金额
            const newAccount = generatePubKey({
              fromPublicKey: keypair.publicKey,
              programId: TOKEN_PROGRAM_ID
            });
            
            // 包装完整的转账金额，而不是只包装差额
            const wrapInstructions = await createWrapSolAccountInstructions(connection, keypair.publicKey, keypair.publicKey, transferAmount, newAccount);
            instructions.push(...wrapInstructions);
            
            // 使用新创建的账户作为主要代币账户
            tokenAccount = newAccount.publicKey;
          }
        }
      } catch (error) {
        throw new Error('创建代币账户时出错: ' + (error instanceof Error ? error.message : String(error)));
      }
  
      // 创建用户转账授权 - 确保在包装SOL指令之后
      const userTransferAuthority = Keypair.generate();
      const approveInstruction = createApproveInstruction(tokenAccount, userTransferAuthority.publicKey, keypair.publicKey, BigInt(transferAmount.toString()));
      instructions.push(approveInstruction);
  
      // 创建桥接指令
      const tokenPublicKey = new PublicKey(cTokenAccountState.token);
      const cTokenPublicKey = new PublicKey(CONFIG.SOL_TOKEN.cToken);
      const bridgeInstruction = createBridgeInstruction(
        cTokenPublicKey,
        new PublicKey(CONFIG.BRIDGE.config),
        tokenPublicKey,
        tokenAccount,
        userTransferAuthority.publicKey,
        new PublicKey(cTokenAccountState.token_mint),
        TOKEN_PROGRAM_ID,
        BigInt(transferAmount.toString()),
        iotexTargetAddress,
        new Uint8Array(0),
        new PublicKey(CONFIG.BRIDGE.programId)
      );
      instructions.push(bridgeInstruction);
  
      // 添加优先费用
      const priorityFee = ComputeBudgetProgram.setComputeUnitPrice({
        microLamports: SolanaToIoTeXConfig.PRIORITY_FEE_MICRO_LAMPORTS
      });
      instructions.unshift(priorityFee);
  
      // 获取最新区块哈希和有效高度
      const latestBlockhash = await connection.getLatestBlockhash('finalized');
  
      // 创建交易消息
      const messageV0 = new TransactionMessage({
        payerKey: keypair.publicKey,
        recentBlockhash: latestBlockhash.blockhash,
        instructions: instructions
      }).compileToV0Message();
  
      // 创建版本化交易
      const versionedTransaction = new VersionedTransaction(messageV0);
  
      // 签名交易
      versionedTransaction.sign([keypair, userTransferAuthority]);
  
      console.log('发送跨链转账交易...');
  
      // 发送交易
      const signature = await connection.sendTransaction(versionedTransaction, {
        skipPreflight: false,
        preflightCommitment: 'confirmed',
        maxRetries: SolanaToIoTeXConfig.MAX_RETRIES
      });
  
      Logger.transaction('跨链转账交易已发送', signature);
  
      Logger.info('等待交易确认...');
  
      // 等待交易确认
      const confirmation = await connection.confirmTransaction(
        {
          signature,
          blockhash: latestBlockhash.blockhash,
          lastValidBlockHeight: latestBlockhash.lastValidBlockHeight
        },
        'confirmed'
      );
  
      if (confirmation.value.err) {
        throw new Error('交易失败: ' + JSON.stringify(confirmation.value.err));
      } else {
        Logger.success('跨链转账交易成功确认');
  
        // 获取交易详情和区块高度
        Logger.info('正在获取交易详情...');
        let transaction = await connection.getParsedTransaction(signature, {
          commitment: 'confirmed',
          maxSupportedTransactionVersion: 0
        });
        if (!transaction) {
          transaction = await connection.getParsedTransaction(signature, {
            commitment: 'finalized',
            maxSupportedTransactionVersion: 0
          });
        }
  
        // 提交交易哈希到跨链桥
        await submitTransactionToBridge(signature, transaction);
  
        // 显示新余额
        const newBalance = await connection.getBalance(keypair.publicKey);
        const solDecimals = new BigNumber(10).pow(SolanaToIoTeXConfig.SOL_DECIMALS);
        const newBalanceSOL = new BigNumber(newBalance).dividedBy(solDecimals);
        Logger.balance('新余额', newBalanceSOL.toFixed());
  
        const actualFee = new BigNumber(balance - newBalance - transferAmount.toNumber()).dividedBy(solDecimals);
        Logger.fee('实际交易费用', `${actualFee.toFixed()} SOL`);
      }
    } catch (error: any) {
      Logger.error('跨链转账失败', error.message);
      if (error.code) {
        Logger.error('错误代码', error.code);
      }
      if (error.logs) {
        Logger.error('交易日志', error.logs);
      }
      throw error;
    }
  }
  
  export async function main() {
    // await transferSOLFromSolanaToIoTeX(
    //   '2P2nX7fuTxH5mxF5V8RM69t75MKwhoUghpz9znida5tAQujYpGWvLN3uV2PGBdqJju8iFZo11DrPAaPkxP6RrEqf',
    //   '0xd12477a364886b979c2cc25f79a51d35ad0bf53d',
    //   0.01
    // )
    await manualSubmitToBridge(
      '5Uvvs7BYBzcxBmJN2tsoainor2FwqXu1NtXoveYUSkFTD7wm3P4wcbek4i15rtfeeEHRAxgPKwPaJ5rdaUcjpdkY'
    )

    console.log('done');
  }
