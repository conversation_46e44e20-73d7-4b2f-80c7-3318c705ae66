import { ethers } from "ethers@6.15.0";
import BigNumber from "bignumber.js@9.3.1";

interface ValidatedArgs {
  privateKey: string;
  solanaTargetAddress: string;
}

interface ProviderAndWallet {
  provider: ethers.JsonRpcProvider;
  wallet: ethers.Wallet;
}

interface Contracts {
  tokenContract: ethers.Contract;
  cashierContract: ethers.Contract;
}

// 统一配置
const CONFIG = {
  // IoTeX 网络配置
  IOTEX: {
    RPC_URL: "https://babel-api.mainnet.iotex.io/",
    CHAIN_ID: 4689,
  },

  // Solana 相关配置
  SOLANA: {
    CHAIN_ID: 101,
  },

  // Cashier 合约配置
  CASHIER: {
    ADDRESS: "******************************************",
    ABI: [
      {
        inputs: [
          { internalType: "address", name: "_token", type: "address" },
          { internalType: "string", name: "_to", type: "string" },
          { internalType: "uint256", name: "_amount", type: "uint256" },
          { internalType: "bytes", name: "_data", type: "bytes" },
        ],
        name: "depositTo",
        outputs: [],
        stateMutability: "payable",
        type: "function",
      },
      {
        inputs: [],
        name: "depositFee",
        outputs: [{ internalType: "uint256", name: "", type: "uint256" }],
        stateMutability: "view",
        type: "function",
      },
    ],
  },

  // SOL 代币配置
  SOL_TOKEN: {
    name: "SOL",
    symbol: "SOL",
    address: "0xa1f3f211d9b33f2086a800842836d67f139b9a7a", // IoTeX 上的 SOL 代币地址
    destAddress: "So11111111111111111111111111111111111111112", // Solana 上的 WSOL 地址
    decimals: 9,
  },

  // ERC20 ABI
  ERC20_ABI: [
    {
      inputs: [{ internalType: "address", name: "account", type: "address" }],
      name: "balanceOf",
      outputs: [{ internalType: "uint256", name: "", type: "uint256" }],
      stateMutability: "view",
      type: "function",
    },
    {
      inputs: [
        { internalType: "address", name: "spender", type: "address" },
        { internalType: "uint256", name: "amount", type: "uint256" },
      ],
      name: "approve",
      outputs: [{ internalType: "bool", name: "", type: "bool" }],
      stateMutability: "nonpayable",
      type: "function",
    },
    {
      inputs: [
        { internalType: "address", name: "owner", type: "address" },
        { internalType: "address", name: "spender", type: "address" },
      ],
      name: "allowance",
      outputs: [{ internalType: "uint256", name: "", type: "uint256" }],
      stateMutability: "view",
      type: "function",
    },
  ],
};

// 辅助函数：验证参数
function validateArgs(
  privateKey: string,
  solanaTargetAddress: string
): ValidatedArgs {
  if (!privateKey || !solanaTargetAddress) {
    throw new Error(
      "使用方法: transferSOLFromIoTeXToSolana(privateKey, solanaTargetAddress)"
    );
  }

  return {
    privateKey,
    solanaTargetAddress,
  };
}

// 辅助函数：验证 Solana 地址格式
function validateSolanaAddress(address: string): void {
  if (address.length < 32 || address.length > 44) {
    throw new Error("Solana 地址格式错误，地址长度应该在 32-44 个字符之间");
  }
}

// 辅助函数：创建提供者和钱包
function createProviderAndWallet(privateKey: string): ProviderAndWallet {
  const provider = new ethers.JsonRpcProvider(CONFIG.IOTEX.RPC_URL);
  const wallet = new ethers.Wallet(privateKey, provider);
  return { provider, wallet };
}

// 辅助函数：创建合约实例
function createContracts(wallet: ethers.Wallet): Contracts {
  const tokenContract = new ethers.Contract(
    CONFIG.SOL_TOKEN.address,
    CONFIG.ERC20_ABI,
    wallet
  );
  const cashierContract = new ethers.Contract(
    CONFIG.CASHIER.ADDRESS,
    CONFIG.CASHIER.ABI,
    wallet
  );
  return { tokenContract, cashierContract };
}

// 辅助函数：检查代币余额
async function checkTokenBalance(
  tokenContract: ethers.Contract,
  walletAddress: string,
  transferAmount: BigNumber
): Promise<ethers.BigNumberish> {
  const balance = await tokenContract.balanceOf(walletAddress);
  const balanceFormatted = new BigNumber(balance.toString()).dividedBy(
    new BigNumber(10).pow(CONFIG.SOL_TOKEN.decimals)
  );
  const transferAmountFormatted = transferAmount.dividedBy(
    new BigNumber(10).pow(CONFIG.SOL_TOKEN.decimals)
  );

  console.log(`当前 SOL 余额: ${balanceFormatted.toFixed()} SOL`);
  console.log(`需要转账金额: ${transferAmountFormatted.toFixed()} SOL`);
  console.log(`原始余额 (wei): ${balance.toString()}`);
  console.log(`原始转账金额 (wei): ${transferAmount.toFixed()}`);

  if (new BigNumber(balance.toString()).isLessThan(transferAmount)) {
    throw new Error(
      `余额不足！当前余额: ${balanceFormatted.toFixed()} SOL，需要转账: ${transferAmountFormatted.toFixed()} SOL`
    );
  }

  return balance;
}

// 辅助函数：处理代币授权
async function handleTokenApproval(
  tokenContract: ethers.Contract,
  walletAddress: string,
  transferAmount: ethers.BigNumberish
): Promise<void> {
  const allowance = await tokenContract.allowance(
    walletAddress,
    CONFIG.CASHIER.ADDRESS
  );
  console.log(
    "当前授权额度:",
    new BigNumber(allowance.toString())
      .dividedBy(new BigNumber(10).pow(CONFIG.SOL_TOKEN.decimals))
      .toFixed(),
    "SOL"
  );

  // 使用 BigNumber 进行比较，兼容 ethers v6
  if (
    new BigNumber(allowance.toString()).isLessThan(
      new BigNumber(transferAmount.toString())
    )
  ) {
    console.log("需要先授权代币...");
    const approveTx = await tokenContract.approve(
      CONFIG.CASHIER.ADDRESS,
      transferAmount
    );
    console.log("授权交易已发送，哈希:", approveTx.hash);
    console.log("等待授权交易确认...");
    await approveTx.wait();
    console.log("✅ 授权成功!");
  }
}

// 辅助函数：检查存款费用和 IOTX 余额
async function checkDepositFeeAndBalance(
  provider: ethers.JsonRpcProvider,
  cashierContract: ethers.Contract,
  walletAddress: string
): Promise<ethers.BigNumberish> {
  const depositFee = await cashierContract.depositFee();
  console.log("存款费用:", ethers.formatEther(depositFee), "IOTX");

  const iotxBalance = await provider.getBalance(walletAddress);
  console.log("当前 IOTX 余额:", ethers.formatEther(iotxBalance), "IOTX");

  if (new BigNumber(iotxBalance.toString()).isLessThan(depositFee)) {
    throw new Error("IOTX 余额不足以支付存款费用");
  }

  return depositFee;
}

/**
 * 从 IoTeX 转账 0.04 SOL 到 Solana 地址的函数
 * @param privateKey - IoTeX 私钥
 * @param target - Solana 目标地址
 */
export async function transferSOLFromIoTeXToSolana(
  privateKey: string,
  target: string,
  inputAmount: number
): Promise<void> {
  // 验证参数
  validateArgs(privateKey, target);

  try {
    // 验证 Solana 地址格式
    validateSolanaAddress(target);

    // 创建提供者和钱包
    const { provider, wallet } = createProviderAndWallet(privateKey);

    console.log("IoTeX 发送方地址:", wallet.address);
    console.log("Solana 接收方地址:", target);

    // 创建合约实例
    const { tokenContract, cashierContract } = createContracts(wallet);

    // 转账金额
    const transferAmount = new BigNumber(inputAmount).multipliedBy(
      new BigNumber(10).pow(CONFIG.SOL_TOKEN.decimals)
    );
    // 注意：transferAmount 已经是最小单位，不需要再用 parseUnits
    const transferAmountEthers = BigInt(transferAmount.toFixed());

    console.log("输入金额:", inputAmount, "SOL");
    console.log("转账金额 (最小单位):", transferAmount.toFixed());
    console.log("ethers 转账金额:", transferAmountEthers.toString());

    // 检查代币余额
    await checkTokenBalance(tokenContract, wallet.address, transferAmount);

    // 处理代币授权
    await handleTokenApproval(
      tokenContract,
      wallet.address,
      transferAmountEthers
    );

    // 检查存款费用和 IOTX 余额
    const depositFee = await checkDepositFeeAndBalance(
      provider,
      cashierContract,
      wallet.address
    );

    // 准备跨链转账参数
    const tokenAddress = CONFIG.SOL_TOKEN.address;
    const recipient = target;
    const amount = transferAmount.toFixed();
    const data = "0x"; // 空数据

    console.log("准备发送跨链转账交易...");
    console.log("代币地址:", tokenAddress);
    console.log("接收地址:", recipient);
    console.log(
      "转账金额:",
      new BigNumber(amount)
        .dividedBy(new BigNumber(10).pow(CONFIG.SOL_TOKEN.decimals))
        .toFixed(),
      "SOL"
    );
    console.log("存款费用:", ethers.formatEther(depositFee), "IOTX");
    console.log("原始转账金额 (wei):", transferAmountEthers.toString());
    console.log("BigNumber转账金额:", amount);

    // 检查转账金额是否合理
    const transferAmountSOL = new BigNumber(amount).dividedBy(
      new BigNumber(10).pow(CONFIG.SOL_TOKEN.decimals)
    );
    if (transferAmountSOL.isGreaterThan(1000)) {
      console.warn(
        "⚠️ 警告: 转账金额过大 (>",
        transferAmountSOL.toFixed(),
        "SOL)，可能导致交易失败"
      );
    }
    if (transferAmountSOL.isLessThan(0.001)) {
      console.warn(
        "⚠️ 警告: 转账金额过小 (<",
        transferAmountSOL.toFixed(),
        "SOL)，可能导致交易失败"
      );
    }

    // 发送跨链转账交易
    const depositTx = await cashierContract.depositTo(
      tokenAddress,
      recipient,
      transferAmountEthers,
      data,
      {
        value: depositFee,
        gasLimit: 300000, // 设置合理的 gas 限制
      }
    );

    console.log("跨链转账交易已发送!");
    console.log("交易哈希:", depositTx.hash);
    console.log("IoTeX 交易链接: https://iotexscan.io/tx/" + depositTx.hash);

    console.log("等待交易确认...");
    const receipt = await depositTx.wait();

    console.log("交易收据状态:", receipt.status);
    console.log("区块号:", receipt.blockNumber);
    console.log("Gas 使用量:", receipt.gasUsed.toString());

    // 检查交易是否成功：只有状态码为 1 才表示成功
    // 状态码 106 表示 ErrExecutionReverted (执行回滚)
    if (receipt.status === 1) {
      console.log("✅ 跨链转账交易成功确认!");
      console.log(
        "✅ 已发起从 IoTeX 到 Solana 的",
        new BigNumber(amount)
          .dividedBy(new BigNumber(10).pow(CONFIG.SOL_TOKEN.decimals))
          .toFixed(),
        "SOL 跨链转账"
      );
      console.log(
        "📝 请注意: 跨链转账需要一定时间处理，请耐心等待代币到达 Solana 地址"
      );
      console.log("🔍 您可以在 Solana 区块浏览器中查看接收地址的余额变化");
    } else {
      console.error("❌ 交易失败，状态码:", receipt.status);
      if (receipt.status === 106) {
        console.error("❌ 错误类型: ErrExecutionReverted - 合约执行失败");
        console.error(
          "💡 可能原因: 转账金额过高、余额不足、授权问题或合约逻辑错误"
        );
      }
      console.error("交易收据:", JSON.stringify(receipt, null, 2));
      throw new Error(`交易失败，状态码: ${receipt.status}`);
    }
  } catch (error: any) {
    console.error("发生错误:", error.message);
    if (error.code) {
      console.error("错误代码:", error.code);
    }
    if (error.reason) {
      console.error("错误原因:", error.reason);
    }
    throw error;
  }
}

export async function main() {
  // await transferSOLFromIoTeXToSolana(
  //   "eaf3bf892b66862489338ac05a54beb902a84e6e2e90db6ad4b6ec7f07571b1a",
  //   "57hfxmbXd2SC5YESxT2bL8mSG2adoV8UJpSWSSNCxJjK",
  //   0.01
  // );

  console.log('done')
}
