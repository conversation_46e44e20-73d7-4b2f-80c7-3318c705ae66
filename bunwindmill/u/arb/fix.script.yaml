summary: ''
description: ''
lock: '!inline u/arb/fix.script.lock'
has_preprocessor: false
kind: script
no_main_func: false
schema:
  $schema: 'https://json-schema.org/draft/2020-12/schema'
  type: object
  properties:
    config:
      type: object
      description: ''
      default: null
      properties:
        iotexPrivateKey:
          type: string
          description: ''
          originalType: string
        key:
          type: string
          description: ''
          originalType: string
        secret:
          type: string
          description: ''
          originalType: string
        solanaPrivateKey:
          type: string
          description: ''
          originalType: string
  required:
    - config
