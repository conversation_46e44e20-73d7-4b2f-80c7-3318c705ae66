import { MimoService } from "../libs/mimo.ts";
import { getDiff } from "./monitor.ts";

/**
 * 1. 有的时候，流程会执行出错，这个流程是用来对流程做补充检查的
 */
export async function main(config: {
    solanaPrivateKey?: string;
    iotexPrivateKey?: string;
    key?: string;
    secret?: string;
}) { 

    const direction = await getDiff();
    if (direction > 50) {
        // 
    } else if (direction < -50) {
        // 
    }

    const mimo = new MimoService(config.iotexPrivateKey);
    const iotxBalance = await mimo.getTokenBalanceFormatted('IOTX');
    const solBalance = await mimo.getTokenBalanceFormatted('SOL');

    console.log(iotxBalance, solBalance);
}