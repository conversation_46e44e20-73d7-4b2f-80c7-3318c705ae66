import { manualSubmitToBridge } from '../libs/soltoiotx.ts';

/**
 * 手动提交交易到跨链桥的示例
 * 
 * 使用场景：
 * 1. 跨链转账交易已经在 Solana 上成功执行，但由于网络问题未能自动提交到跨链桥
 * 2. 需要重新提交之前的跨链转账交易
 * 3. 批量处理多个跨链转账交易的提交
 */

/**
 * 单个交易提交示例
 */
async function submitSingleTransaction() {
  console.log('=== 单个交易提交示例 ===');
  
  // 替换为你的实际交易签名
  const transactionSignature = '61HAim7GX1tLvzdwNhKKzEo8ByBmSWkvPbvptqsnKwqZmSN2fB2aLT1ZgMNV8HKDdLRHHMjZJLujEqnvr5r19gUK';
  
  try {
    const result = await manualSubmitToBridge(transactionSignature);
    
    if (result.success) {
      console.log('✅ 提交成功!');
      console.log('📝 交易哈希:', result.txHash);
      console.log('🏗️  区块高度:', result.blockHeight);
      console.log('💬 消息:', result.message);
    } else {
      console.log('❌ 提交失败!');
      console.log('💬 错误信息:', result.message);
    }
  } catch (error) {
    console.error('❌ 执行出错:', error);
  }
}

/**
 * 批量交易提交示例
 */
async function submitMultipleTransactions() {
  console.log('\n=== 批量交易提交示例 ===');
  
  // 替换为你的实际交易签名列表
  const transactionSignatures = [
    '61HAim7GX1tLvzdwNhKKzEo8ByBmSWkvPbvptqsnKwqZmSN2fB2aLT1ZgMNV8HKDdLRHHMjZJLujEqnvr5r19gUK',
    // 'another_transaction_signature_here',
    // 'yet_another_transaction_signature_here',
  ];
  
  const results = [];
  
  for (let i = 0; i < transactionSignatures.length; i++) {
    const signature = transactionSignatures[i];
    console.log(`\n处理交易 ${i + 1}/${transactionSignatures.length}: ${signature.substring(0, 20)}...`);
    
    try {
      const result = await manualSubmitToBridge(signature);
      results.push({ signature, result });
      
      if (result.success) {
        console.log(`✅ 交易 ${i + 1} 提交成功`);
      } else {
        console.log(`❌ 交易 ${i + 1} 提交失败: ${result.message}`);
      }
      
      // 添加延迟避免请求过于频繁
      if (i < transactionSignatures.length - 1) {
        console.log('⏳ 等待 2 秒...');
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    } catch (error) {
      console.error(`❌ 交易 ${i + 1} 执行出错:`, error);
      results.push({ signature, result: { success: false, message: String(error) } });
    }
  }
  
  // 汇总结果
  console.log('\n=== 批量提交结果汇总 ===');
  const successCount = results.filter(r => r.result.success).length;
  const failCount = results.length - successCount;
  
  console.log(`📊 总计: ${results.length} 个交易`);
  console.log(`✅ 成功: ${successCount} 个`);
  console.log(`❌ 失败: ${failCount} 个`);
  
  if (failCount > 0) {
    console.log('\n❌ 失败的交易:');
    results.filter(r => !r.result.success).forEach((item, index) => {
      console.log(`${index + 1}. ${item.signature.substring(0, 20)}... - ${item.result.message}`);
    });
  }
}

/**
 * 使用自定义 RPC 节点的示例
 */
async function submitWithCustomRpc() {
  console.log('\n=== 使用自定义 RPC 节点示例 ===');
  
  const transactionSignature = '61HAim7GX1tLvzdwNhKKzEo8ByBmSWkvPbvptqsnKwqZmSN2fB2aLT1ZgMNV8HKDdLRHHMjZJLujEqnvr5r19gUK';
  
  // 使用自定义的 RPC 节点（例如更快的付费节点）
  const customRpcUrl = 'https://api.mainnet-beta.solana.com'; // 替换为你的 RPC URL
  
  try {
    const result = await manualSubmitToBridge(transactionSignature, customRpcUrl);
    
    console.log('使用自定义 RPC 节点的提交结果:', result);
  } catch (error) {
    console.error('❌ 使用自定义 RPC 节点时出错:', error);
  }
}

/**
 * 主函数 - 运行所有示例
 */
async function main() {
  console.log('🚀 手动提交交易到跨链桥示例');
  console.log('=====================================');
  
  try {
    // 运行单个交易提交示例
    await submitSingleTransaction();
    
    // 运行批量交易提交示例
    await submitMultipleTransactions();
    
    // 运行自定义 RPC 示例
    await submitWithCustomRpc();
    
  } catch (error) {
    console.error('❌ 主程序执行出错:', error);
  }
  
  console.log('\n🎉 所有示例执行完成!');
}

/**
 * 工具函数：验证交易签名格式
 */
function validateTransactionSignature(signature: string): boolean {
  if (!signature || typeof signature !== 'string') {
    return false;
  }
  
  // Solana 交易签名通常是 88 个字符的 base58 字符串
  if (signature.length < 80 || signature.length > 90) {
    return false;
  }
  
  // 检查是否只包含 base58 字符
  const base58Regex = /^[1-9A-HJ-NP-Za-km-z]+$/;
  return base58Regex.test(signature);
}

/**
 * 工具函数：从 Solscan URL 提取交易签名
 */
function extractSignatureFromSolscanUrl(url: string): string | null {
  try {
    // 支持的 URL 格式:
    // https://solscan.io/tx/61HAim7GX1tLvzdwNhKKzEo8ByBmSWkvPbvptqsnKwqZmSN2fB2aLT1ZgMNV8HKDdLRHHMjZJLujEqnvr5r19gUK
    // https://explorer.solana.com/tx/61HAim7GX1tLvzdwNhKKzEo8ByBmSWkvPbvptqsnKwqZmSN2fB2aLT1ZgMNV8HKDdLRHHMjZJLujEqnvr5r19gUK
    
    const match = url.match(/\/tx\/([1-9A-HJ-NP-Za-km-z]{80,90})/);
    return match ? match[1] : null;
  } catch (error) {
    return null;
  }
}

// 如果直接运行此文件，执行主函数
if (import.meta.main) {
  main().catch(console.error);
}

// 导出函数供其他模块使用
export {
  submitSingleTransaction,
  submitMultipleTransactions,
  submitWithCustomRpc,
  validateTransactionSignature,
  extractSignatureFromSolscanUrl
};
